{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "skipDefaultLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client"]}, "include": ["src", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"], "references": [{"path": "./tsconfig.node.json"}]}